'use server'

import { auth } from '@/lib/auth'
import { isUserAdminServer } from '@/lib/admin-utils-server'
import { revalidatePath } from 'next/cache'

/**
 * Server action to list users (admin only)
 */
export async function listUsersAction(params?: {
  searchValue?: string
  searchField?: 'email' | 'name'
  limit?: number
  offset?: number
  sortBy?: string
  sortDirection?: 'asc' | 'desc'
}) {
  try {
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers()),
    })

    if (!session?.user) {
      return { success: false, error: 'Not authenticated' }
    }

    const isAdmin = await isUserAdminServer(session.user.id)
    if (!isAdmin) {
      return { success: false, error: 'Insufficient permissions' }
    }

    // For now, return a placeholder since we don't have a list users endpoint
    // You can implement this later using your custom API endpoint
    return {
      success: true,
      data: {
        users: [],
        total: 0,
        limit: params?.limit || 50,
        offset: params?.offset || 0,
      },
    }
  } catch (error) {
    console.error('Error listing users:', error)
    return { success: false, error: 'Internal server error' }
  }
}

/**
 * Server action to impersonate a user (admin only)
 */
export async function impersonateUserAction(userId: string) {
  try {
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers()),
    })

    if (!session?.user) {
      return { success: false, error: 'Not authenticated' }
    }

    const isAdmin = await isUserAdminServer(session.user.id)
    if (!isAdmin) {
      return { success: false, error: 'Insufficient permissions' }
    }

    const result = await auth.api.impersonateUser({
      body: { userId },
      headers: await import('next/headers').then(h => h.headers()),
    })

    // Better Auth impersonateUser returns session data directly
    if (result) {
      revalidatePath('/')
      return { success: true, data: result }
    }

    return {
      success: false,
      error: 'Failed to impersonate user',
    }
  } catch (error) {
    console.error('Error impersonating user:', error)
    return { success: false, error: 'Internal server error' }
  }
}

/**
 * Server action to stop impersonating a user (admin only)
 */
export async function stopImpersonatingAction() {
  try {
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers()),
    })

    if (!session?.user) {
      return { success: false, error: 'Not authenticated' }
    }

    const isAdmin = await isUserAdminServer(session.user.id)
    if (!isAdmin) {
      return { success: false, error: 'Insufficient permissions' }
    }

    // For now, return a placeholder since we don't have a stop impersonating endpoint
    // You can implement this later using your custom API endpoint
    return { success: false, error: 'Stop impersonating not implemented yet' }
  } catch (error) {
    console.error('Error stopping impersonation:', error)
    return { success: false, error: 'Internal server error' }
  }
}

/**
 * Server action to ban a user (admin only)
 */
export async function banUserAction(data: {
  userId: string
  banReason?: string
  banExpiresIn?: number
}) {
  try {
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers()),
    })

    if (!session?.user) {
      return { success: false, error: 'Not authenticated' }
    }

    const isAdmin = await isUserAdminServer(session.user.id)
    if (!isAdmin) {
      return { success: false, error: 'Insufficient permissions' }
    }

    const result = await auth.api.banUser({
      body: data,
      headers: await import('next/headers').then(h => h.headers()),
    })

    // Better Auth banUser returns user data directly
    if (result) {
      revalidatePath('/admin')
      return { success: true }
    }

    return {
      success: false,
      error: 'Failed to ban user',
    }
  } catch (error) {
    console.error('Error banning user:', error)
    return { success: false, error: 'Internal server error' }
  }
}

/**
 * Server action to unban a user (admin only)
 */
export async function unbanUserAction(userId: string) {
  try {
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers()),
    })

    if (!session?.user) {
      return { success: false, error: 'Not authenticated' }
    }

    const isAdmin = await isUserAdminServer(session.user.id)
    if (!isAdmin) {
      return { success: false, error: 'Insufficient permissions' }
    }

    const result = await auth.api.unbanUser({
      body: { userId },
      headers: await import('next/headers').then(h => h.headers()),
    })

    // Better Auth unbanUser returns user data directly
    if (result) {
      revalidatePath('/admin')
      return { success: true }
    }

    return {
      success: false,
      error: 'Failed to unban user',
    }
  } catch (error) {
    console.error('Error unbanning user:', error)
    return { success: false, error: 'Internal server error' }
  }
}

/**
 * Server action to set user role (admin only)
 */
export async function setUserRoleAction(data: {
  userId: string
  role: string
}) {
  try {
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers()),
    })

    if (!session?.user) {
      return { success: false, error: 'Not authenticated' }
    }

    const isAdmin = await isUserAdminServer(session.user.id)
    if (!isAdmin) {
      return { success: false, error: 'Insufficient permissions' }
    }

    const result = await auth.api.setRole({
      body: {
        userId: data.userId,
        role: data.role as 'admin' | 'user',
      },
      headers: await import('next/headers').then(h => h.headers()),
    })

    // Better Auth setRole returns user data directly
    if (result) {
      revalidatePath('/admin')
      return { success: true }
    }

    return {
      success: false,
      error: 'Failed to set user role',
    }
  } catch (error) {
    console.error('Error setting user role:', error)
    return { success: false, error: 'Internal server error' }
  }
}

/**
 * Server action to check if user has admin permissions
 */
export async function checkAdminPermissionsAction(userId?: string) {
  try {
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers()),
    })

    if (!session?.user) {
      return { success: false, error: 'Not authenticated' }
    }

    const targetUserId = userId || session.user.id
    const isAdmin = await isUserAdminServer(targetUserId)

    return { success: true, isAdmin }
  } catch (error) {
    console.error('Error checking admin permissions:', error)
    return { success: false, error: 'Internal server error' }
  }
}

/**
 * Server action to create a new user (admin only)
 */
export async function createUserAction(data: {
  name: string
  email: string
  password: string
}) {
  try {
    const session = await auth.api.getSession({
      headers: await import('next/headers').then(h => h.headers()),
    })

    if (!session?.user) {
      return { success: false, error: 'Not authenticated' }
    }

    const isAdmin = await isUserAdminServer(session.user.id)
    if (!isAdmin) {
      return { success: false, error: 'Insufficient permissions' }
    }

    // Get auth context for direct database operations
    const ctx = await auth.$context

    // Check if user already exists
    const existingUser = await ctx.adapter.findOne({
      model: 'user',
      where: [{ field: 'email', value: data.email }],
    })

    if (existingUser) {
      return { success: false, error: 'User with this email already exists' }
    }

    // Generate unique user ID
    const userId = crypto.randomUUID()

    // Hash the password using better-auth's password hashing
    const hashedPassword = await ctx.password.hash(data.password)

    // Create user directly in database without triggering email verification
    const createdUser = await ctx.adapter.create({
      model: 'user',
      data: {
        id: userId,
        name: data.name,
        email: data.email,
        emailVerified: true, // Admin-created users are pre-verified
        role: 'user', // Default role
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })

    // Create password account for the user
    await ctx.adapter.create({
      model: 'account',
      data: {
        id: crypto.randomUUID(),
        userId: userId,
        providerId: 'credential',
        accountId: data.email,
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    })

    revalidatePath('/admin')
    return { success: true, data: createdUser }
  } catch (error) {
    console.error('Error creating user:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create user',
    }
  }
}
