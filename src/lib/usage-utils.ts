import { db } from '@/lib/db'
import { usage, subscription } from '@/db/schema'
import { eq, sql, and } from 'drizzle-orm'
import { getPlanLimits as getPlanLimitsFromUtils } from '@/lib/plan-utils'
import { getBillingPeriod } from '@/lib/subscription-utils'

export type UsageType =
  | 'projects'
  | 'videoExports'
  | 'aiImages'
  | 'teamMembers'
  | 'storage'

// Simple function to increment usage by getting current value and updating
export async function incrementUsage(
  organizationId: string,
  usageType: UsageType,
  amount: number = 1
) {
  try {
    // Get current usage record for the organization
    const currentRecord = await db
      .select()
      .from(usage)
      .where(eq(usage.organizationId, organizationId))
      .limit(1)

    if (currentRecord.length === 0) {
      return { success: false, error: 'Usage record not found' }
    }

    const current = currentRecord[0]
    let newValue = 0

    // Update the appropriate field
    switch (usageType) {
      case 'projects':
        newValue = (current.projectsUsed || 0) + amount
        await db
          .update(usage)
          .set({ projectsUsed: newValue, updatedAt: new Date() })
          .where(eq(usage.organizationId, organizationId))
        break
      case 'videoExports':
        newValue = (current.videoExportsUsed || 0) + amount
        await db
          .update(usage)
          .set({ videoExportsUsed: newValue, updatedAt: new Date() })
          .where(eq(usage.organizationId, organizationId))
        break
      case 'aiImages':
        newValue = (current.aiImagesUsed || 0) + amount
        await db
          .update(usage)
          .set({ aiImagesUsed: newValue, updatedAt: new Date() })
          .where(eq(usage.organizationId, organizationId))
        break
      case 'teamMembers':
        newValue = (current.teamMembersUsed || 0) + amount
        await db
          .update(usage)
          .set({ teamMembersUsed: newValue, updatedAt: new Date() })
          .where(eq(usage.organizationId, organizationId))
        break
      case 'storage':
        // Convert bytes to MB (1 MB = 1024 * 1024 bytes)
        const amountInMB = Math.ceil(amount / (1024 * 1024))
        newValue = (current.storageUsed || 0) + amountInMB
        await db
          .update(usage)
          .set({ storageUsed: newValue, updatedAt: new Date() })
          .where(eq(usage.organizationId, organizationId))
        break
    }

    return { success: true }
  } catch (error) {
    console.error(`Error incrementing ${usageType} usage:`, error)
    return { success: false, error }
  }
}

// Check if organization can perform an action based on their usage limits
export async function checkUsageLimit(
  organizationId: string,
  usageType: UsageType
): Promise<{ allowed: boolean; current: number; limit: number }> {
  try {
    const usageRecord = await db
      .select()
      .from(usage)
      .where(eq(usage.organizationId, organizationId))
      .limit(1)

    if (!usageRecord || usageRecord.length === 0) {
      return { allowed: true, current: 0, limit: -1 }
    }

    const currentUsage = usageRecord[0]
    let currentValue = 0

    // Get current value
    switch (usageType) {
      case 'projects':
        currentValue = currentUsage.projectsUsed || 0
        break
      case 'videoExports':
        currentValue = currentUsage.videoExportsUsed || 0
        break
      case 'aiImages':
        currentValue = currentUsage.aiImagesUsed || 0
        break
      case 'teamMembers':
        currentValue = currentUsage.teamMembersUsed || 0
        break
      case 'storage':
        currentValue = currentUsage.storageUsed || 0
        break
    }

    // Determine plan type and billing period from active subscription if present
    let planType = currentUsage.planType
    let periodStart = currentUsage.currentPeriodStart
    let periodEnd = currentUsage.currentPeriodEnd

    try {
      const activeSubscription = await db
        .select()
        .from(subscription)
        .where(
          and(
            eq(subscription.referenceId, organizationId),
            sql`${subscription.status} IN ('active', 'trialing')`,
            sql`${subscription.periodEnd} > NOW()`,
            sql`(${subscription.cancelAtPeriodEnd} IS NULL OR ${subscription.cancelAtPeriodEnd} = false)`
          )
        )
        .orderBy(sql`${subscription.periodStart} DESC`)
        .limit(1)

      if (activeSubscription.length > 0) {
        const sub = activeSubscription[0]
        planType = sub.plan
        periodStart = sub.periodStart || currentUsage.currentPeriodStart
        periodEnd = sub.periodEnd || currentUsage.currentPeriodEnd
      }
    } catch {
      // Fallback to stored usage plan if subscription lookup fails
    }

    const billingPeriod = getBillingPeriod(
      periodStart || undefined,
      periodEnd || undefined
    )
    const planLimits = getPlanLimits(planType, billingPeriod)
    let limit = 0

    switch (usageType) {
      case 'projects':
        limit = planLimits.projects
        break
      case 'videoExports':
        limit = planLimits.videoExports
        break
      case 'aiImages':
        limit = planLimits.aiImages
        break
      case 'teamMembers':
        limit = planLimits.teamMembers
        break
      case 'storage':
        limit = planLimits.storage
        break
    }

    // -1 means unlimited
    if (limit === -1) {
      return { allowed: true, current: currentValue, limit: -1 }
    }

    return {
      allowed: currentValue < limit,
      current: currentValue,
      limit,
    }
  } catch (error) {
    console.error(`Error checking ${usageType} usage limit:`, error)
    return { allowed: false, current: 0, limit: 0 }
  }
}

// Helper function to get usage for a specific user (for backward compatibility)
export async function getUserUsage(userId: string) {
  try {
    // Find usage record where the user is a member
    const usageRecord = await db
      .select()
      .from(usage)
      .where(sql`${userId} = ANY(${usage.members})`)
      .limit(1)

    return usageRecord[0] || null
  } catch (error) {
    console.error('Error getting user usage:', error)
    return null
  }
}

// Helper function to add a user to an organization's usage record
export async function addUserToUsage(organizationId: string, userId: string) {
  try {
    await db
      .update(usage)
      .set({
        members: sql`array_append(${usage.members}, ${userId})`,
        updatedAt: new Date(),
      })
      .where(eq(usage.organizationId, organizationId))

    return { success: true }
  } catch (error) {
    console.error('Error adding user to usage:', error)
    return { success: false, error }
  }
}

// Helper function to remove a user from an organization's usage record
export async function removeUserFromUsage(
  organizationId: string,
  userId: string
) {
  try {
    await db
      .update(usage)
      .set({
        members: sql`array_remove(${usage.members}, ${userId})`,
        updatedAt: new Date(),
      })
      .where(eq(usage.organizationId, organizationId))

    return { success: true }
  } catch (error) {
    console.error('Error removing user from usage:', error)
    return { success: false, error }
  }
}

function getPlanLimits(planType: string, period?: 'monthly' | 'annual' | '') {
  return getPlanLimitsFromUtils(planType, period)
}

// Reset usage for an organization when subscription is created/updated
export async function resetUsageForSubscription(
  referenceId: string,
  planType: string,
  periodEndDate?: Date
) {
  try {
    const now = new Date()
    const endDate =
      periodEndDate || new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)

    // Only update existing usage record - don't insert new ones
    await db
      .update(usage)
      .set({
        // Don't update members array to preserve existing team members
        projectsUsed: 0,
        videoExportsUsed: 0,
        aiImagesUsed: 0,
        teamMembersUsed: 1,
        storageUsed: 0,
        planType: planType.toLowerCase(),
        planStatus: 'active',
        currentPeriodStart: now,
        currentPeriodEnd: endDate,
        updatedAt: now,
      })
      .where(eq(usage.organizationId, referenceId))

    console.log(
      `Usage reset for organization ${referenceId} with plan ${planType}`
    )

    return { success: true }
  } catch (error) {
    console.error('Error resetting usage for subscription:', error)
    return { success: false, error }
  }
}
