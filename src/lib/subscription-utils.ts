/**
 * Utility functions for subscription management
 */

/**
 * Determines the billing period (monthly or annual) based on subscription period dates
 * @param periodStart - Start date of the subscription period
 * @param periodEnd - End date of the subscription period
 * @returns 'monthly' | 'annual'
 */
export function getBillingPeriod(
  periodStart?: string | Date,
  periodEnd?: string | Date
): 'monthly' | 'annual' | '' {
  if (!periodStart || !periodEnd) {
    return ''
  }

  const start = new Date(periodStart)
  const end = new Date(periodEnd)

  // Calculate the difference in days
  const diffTime = end.getTime() - start.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  // If the period is more than 31 days, it's annual, otherwise monthly
  return diffDays > 32 ? 'annual' : 'monthly'
}
