# Tech Context

## Technologies used

### Frontend Core

- **Next.js 15.3.2** - Modern React framework with App Router, Turbopack support, and advanced optimizations
- **React 19.0.0** - Latest React with concurrent features and improved performance
- **TypeScript 5** - Full type safety with strict configuration and advanced type checking

### UI/Design System

- **shadcn/ui** - Modern, accessible UI component library with Radix UI primitives
- **Tailwind CSS 4** - Utility-first CSS framework with custom design tokens
- **Radix UI** - Headless UI components for accessibility and customization
- **Lucide React** - Consistent icon library with 500+ icons
- **Framer Motion** - Advanced animations and transitions
- **next-themes** - Theme management with system preference detection

### Video Processing & Rendering

- **Remotion 4.0.323** - React-based video creation framework
- **@remotion/cli** - Command-line tools for video rendering
- **@remotion/cloudrun** - Google Cloud Run rendering support
- **@remotion/lambda** - AWS Lambda rendering support
- **@remotion/player** - Video player component for previews
- **@remotion/renderer** - Server-side video rendering

### Authentication & Authorization

- **Better Auth 1.3.4** - Modern authentication with organization support
- **@better-auth/stripe** - Stripe integration for subscriptions
- **better-auth-harmony** - ESM compatibility layer
- **Google OAuth** - Social authentication integration

### Database & ORM

- **PostgreSQL** - Primary database with advanced features
- **Drizzle ORM 0.44.2** - Type-safe database operations with migrations
- **drizzle-kit** - Database schema management and migrations

### State Management

- **Zustand 5.0.5** - Lightweight state management for video editor
- **@tanstack/react-query 5.80.6** - Server state management with caching
- **React Hook Form 7.60.0** - Form state management with validation

### Media & Content Processing

- **ElevenLabs API** - AI voice synthesis and speech generation
- **OpenAI API** - Text processing and content generation
- **Pexels API** - Stock images and videos
- **Unsplash API** - High-quality stock photography
- **Getty Images API** - Premium stock content
- **Giphy API** - Animated GIFs and stickers
- **Tenor API** - GIF search and integration
- **Pixabay API** - Free stock media
- **YouTube Data API** - Video publishing and management

### File Processing

- **@mozilla/readability** - Content extraction from web pages
- **turndown** - HTML to Markdown conversion
- **unpdf** - PDF text extraction
- **ffprobe** - Audio/video metadata extraction
- **form-data** - File upload handling

### Background Processing

- **Inngest 3.39.2** - Background job processing and workflows
- **inngest-cli** - Development tools for background functions

### Analytics & Monitoring

- **PostHog** - Product analytics and feature flags
- **Crisp** - Customer support chat integration

### Development Tools

- **ESLint 9** - Code linting with Next.js configuration
- **Prettier 3.5.3** - Code formatting
- **Bun** - Fast local development runtime and package manager
- **npm** - Production package management
- **Docker** - Containerization for deployment

## Development setup

### Prerequisites

- **Bun** (Latest version) - For local development and package management
- **Node.js** (Latest LTS version) - For cloud hosting and production
- **PostgreSQL** - Database server (local or cloud)
- **Git** - Version control

### Package Manager Strategy

- **Bun** for local development (faster performance and package installation)
- **npm** for cloud hosting and Dockerfile (better compatibility)
- **package-lock.json** maintained for npm compatibility
- **bun.lockb** for local development caching

### Environment Configuration

- **Development**: Local PostgreSQL with Bun runtime
- **Production**: Cloud PostgreSQL with Node.js runtime
- **Environment Variables**: Comprehensive .env configuration for all services
- **Docker**: Containerized deployment with optimized build process

### Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── (dashboard)/       # Main application routes
│   ├── (scene-editor)/    # Video editor routes
│   └── api/               # API routes
├── components/            # Reusable UI components
│   ├── ui/               # shadcn/ui components
│   └── [feature]/        # Feature-specific components
├── lib/                  # Utility functions and configurations
├── hooks/                # Custom React hooks
├── store/                # Zustand state management
├── types/                # TypeScript type definitions
└── db/                   # Database schema and queries
```

### Development Workflow

- **Hot Reload**: Next.js development server with Turbopack
- **Type Checking**: Real-time TypeScript validation
- **Linting**: ESLint with Next.js configuration
- **Formatting**: Prettier with consistent code style
- **Database**: Drizzle migrations and schema management
- **Background Jobs**: Inngest development server for testing workflows

## Technical constraints

### Browser Support

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+
- **Progressive Enhancement**: Graceful degradation for older browsers
- **WebGL Support**: Required for video rendering and previews

### Performance Requirements

- **Initial Page Load**: < 3 seconds on 3G connection
- **Video Processing**: Real-time preview with < 1 second latency
- **Bundle Size**: < 500KB initial JavaScript bundle
- **Memory Usage**: < 100MB for video editor operations
- **Mobile Performance**: 60fps animations and smooth scrolling

### Security Considerations

- **Authentication**: JWT-based sessions with secure cookies
- **Data Validation**: Zod schemas for all API inputs
- **File Upload**: Size limits, type validation, and virus scanning
- **API Security**: Rate limiting, CORS, and input sanitization
- **Content Security**: XSS protection and secure headers

### Scalability Considerations

- **Database**: PostgreSQL with connection pooling
- **File Storage**: Cloud storage with CDN for media assets
- **Video Rendering**: Distributed rendering with AWS Lambda/Google Cloud Run
- **Background Jobs**: Inngest for scalable job processing
- **Caching**: Redis for session storage and API caching

## Key Dependencies

### Production Dependencies

- **next@15.3.2** - React framework
- **react@19.0.0** - UI library
- **typescript@5** - Type safety
- **@remotion/cli@4.0.323** - Video rendering
- **better-auth@1.3.4** - Authentication
- **drizzle-orm@0.44.2** - Database ORM
- **zustand@5.0.5** - State management
- **@tanstack/react-query@5.80.6** - Server state
- **tailwindcss@4** - Styling
- **zod@3.22.4** - Validation

### Development Dependencies

- **@types/react@19** - React types
- **@types/node@20** - Node.js types
- **eslint@9** - Code linting
- **prettier@3.5.3** - Code formatting
- **drizzle-kit@0.31.1** - Database migrations
- **inngest-cli@1.8.0** - Background job development
