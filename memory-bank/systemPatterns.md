# System Patterns

## System architecture

### Frontend Architecture

1. **Next.js 15 App Router**
   - **Server and Client Components**: Proper separation with "use client" directives
   - **API Routes**: Comprehensive REST API for all backend operations
   - **Route Groups**:
     - `(auth)`: Authentication routes (sign-in, sign-up, password reset)
     - `(dashboard)`: Main application with nested routes
     - `(scene-editor)`: Video editor with specialized layout
   - **File-based Routing**: Intuitive URL structure with nested layouts
   - **Page Configuration**: Dynamic metadata and title mapping

2. **Component Architecture**
   - **Feature-based Organization**: Components grouped by functionality
   - **Atomic Design**: Reusable components with consistent interfaces
   - **shadcn/ui Integration**: Accessible, customizable UI components
   - **Layout Separation**: Clear distinction between layout and content components
   - **Index Exports**: Clean import paths with organized barrel exports

3. **State Management Strategy**
   - **Zustand**: Global state for video editor (scenes, project data, UI state)
   - **React Query**: Server state management with caching and synchronization
   - **React Hook Form**: Form state with validation
   - **Local Storage**: User preferences and editor settings
   - **Better Auth**: Authentication and session state

### Data Flow & Component Architecture

1. **Video Editor Architecture**

   ```
   SceneEditorPage
   ├── VideoPreviewLayout
   │   ├── VideoEditorHeader (export, recent exports)
   │   ├── ScenesSidebar (scene management)
   │   └── VideoPlayerContainer (preview + controls)
   ├── Modal System (export, recent exports)
   └── Keyboard Shortcuts
   ```

2. **Content Creation Workflows**

   ```
   CreateVideoPage
   ├── ConversionGrid (content type selection)
   ├── VideoForm (content input and configuration)
   ├── MediaPicker (stock media selection)
   ├── VoicePicker (ElevenLabs voice selection)
   └── AdvancedSettings (duration, pace, etc.)
   ```

3. **Usage & Billing System**

   ```
   BillingPage
   ├── SubscriptionCard (current plan)
   ├── UsageMetrics (real-time usage display)
   ├── BillingToggle (monthly/annual)
   ├── PricingTable (plan comparison)
   └── UpgradeModal (plan upgrade flow)
   ```

4. **Layout Architecture**

   ```
   RootLayout
   ├── ThemeProvider
   ├── QueryProvider
   ├── PrefetchProvider
   └── AuthAwareStartup
       ├── DashboardLayout
       │   ├── AppSidebar (navigation + usage)
       │   ├── DashboardHeader (page titles)
       │   └── Main Content
       └── ModalProvider
   ```

## Key technical decisions

1. **Video Processing Architecture**
   - **Remotion 4.0**: React-based video rendering with cloud support
   - **Dual Rendering**: Local development + cloud production rendering
   - **Scene-based Editing**: Modular video composition with individual scene management
   - **Real-time Preview**: Live video preview with Remotion Player
   - **Background Processing**: Inngest for scalable video rendering jobs

2. **Authentication & Organization System**
   - **Better Auth**: Modern authentication with organization support
   - **Multi-tenant Architecture**: Organization-based data isolation
   - **Role-based Access**: Owner, admin, member roles with proper permissions
   - **Automatic Organization Creation**: Domain-based organization setup
   - **Session Management**: Active organization tracking in sessions

3. **Database & State Management**
   - **Drizzle ORM**: Type-safe database operations with PostgreSQL
   - **Usage Tracking**: Real-time usage monitoring with plan limits
   - **Zustand Store**: Optimized video editor state management
   - **React Query**: Server state caching and synchronization
   - **Local Storage**: User preferences and editor settings persistence

4. **Content Processing Workflows**
   - **Multi-format Support**: Text, blog, PDF, audio, podcast to video
   - **AI Integration**: OpenAI for content processing, ElevenLabs for voice
   - **Media APIs**: Comprehensive stock media integration
   - **File Processing**: Advanced file handling with validation
   - **YouTube Integration**: Direct publishing with OAuth

5. **UI/UX Design System**
   - **shadcn/ui**: Accessible, customizable component library
   - **Tailwind CSS 4**: Modern styling with oklch color space
   - **Theme System**: Dark/light mode with system preference detection
   - **Responsive Design**: Mobile-first approach with touch-friendly interfaces
   - **Micro-interactions**: Professional hover effects and transitions

## Design patterns

1. **Component Composition**
   - Small, focused components with single responsibilities
   - Composition over inheritance
   - Props-based configuration
   - Proper TypeScript interfaces for all components

2. **Card Component Pattern**

   ```tsx
   <Card className="hover:shadow-md transition-all">
     <CardHeader>
       <Icon + Title + Description>
     </CardHeader>
     <CardContent>
       <Content Area>
     </CardContent>
     <CardFooter>
       <Action Button>
     </CardFooter>
   </Card>
   ```

3. **Theme Integration Pattern**
   - All components support dark/light themes
   - Consistent color variables across components
   - Proper contrast ratios and accessibility

4. **Responsive Design Pattern**
   - Mobile-first CSS classes
   - Responsive grid layouts
   - Adaptive component behavior
   - Touch-friendly interfaces

## Code organization

1. **Directory Structure**

   ```
   src/
   ├── app/
   │   ├── (auth)/          # Authentication routes
   │   ├── (dashboard)/     # Main app routes
   │   │   ├── _components/ # Dashboard-specific components
   │   │   ├── pricing/     # Pricing page and components
   │   │   └── [other]/     # Other feature pages
   │   └── layout.tsx       # Root layout
   ├── components/
   │   ├── ui/              # shadcn/ui components
   │   ├── pricing/         # Pricing feature components
   │   └── [shared]/        # Other shared components
   └── lib/                 # Utility functions
   ```

2. **Component Export Pattern**

   ```tsx
   // index.ts files for clean imports
   export { SubscriptionCard } from './subscription-card'
   export { BillingToggle } from './billing-toggle'
   export type { Plan } from './types'
   ```

3. **Naming Conventions**
   - PascalCase for components and interfaces
   - camelCase for functions and variables
   - kebab-case for file names
   - Descriptive, feature-based naming

## Component relationships

1. **Layout Hierarchy**

   ```
   RootLayout (theme provider)
   └── DashboardLayout (sidebar + header)
       └── Page Components
           └── Feature Components
   ```

2. **Dashboard Component Flow**

   ```
   DashboardPage
   ├── SectionHeader (title + description)
   └── ConversionGrid
       └── ConversionCard[] (individual conversion options)
   ```

3. **Pricing Component Relationships**

   ```
   PricingPage
   ├── SubscriptionCard (standalone)
   ├── BillingToggle (state management)
   ├── PricingTable (desktop layout)
   │   └── PlanCard[] (embedded in table)
   └── PlanCard[] (mobile layout)
   ```

4. **Theme and Provider Integration**
   - ThemeProvider wraps entire application
   - Better Auth handles authentication state
   - ModalProvider manages modal states
   - Proper provider composition in root layout

## Implemented Patterns

1. **Hover Effect System**
   - Consistent hover transitions (300ms duration)
   - Scale transforms for interactive elements
   - Color transitions for better feedback
   - Shadow elevation changes

2. **Responsive Component Pattern**
   - Mobile-first design approach
   - Breakpoint-aware layouts
   - Touch-friendly button sizes
   - Adaptive grid systems

3. **TypeScript Integration**
   - Strong typing for all component props
   - Interface definitions for complex objects
   - Type-safe routing and navigation
   - Proper error handling
